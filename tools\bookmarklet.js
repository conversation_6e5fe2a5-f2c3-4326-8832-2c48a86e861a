// 微信读书Cookie获取书签工具
// 使用方法：
// 1. 登录微信读书网页版 (https://weread.qq.com/)
// 2. 在浏览器地址栏中粘贴下面的代码并回车
// 3. 会弹出一个对话框显示你的Cookie

javascript:(function(){
    // 获取当前页面的Cookie
    var cookie = document.cookie;
    
    // 检查是否在微信读书域名
    if (!window.location.hostname.includes('weread.qq.com')) {
        alert('请在微信读书网页版 (weread.qq.com) 上运行此工具！');
        return;
    }
    
    // 检查是否已登录
    if (!cookie.includes('wr_vid') || !cookie.includes('wr_skey')) {
        alert('请先登录微信读书账号！');
        return;
    }
    
    // 创建一个模态对话框显示Cookie
    var modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        z-index: 10000;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: Arial, sans-serif;
    `;
    
    var content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 10px;
        max-width: 80%;
        max-height: 80%;
        overflow: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;
    
    // 提取用户信息
    var userInfo = '';
    var vidMatch = cookie.match(/wr_vid=(\d+)/);
    var nameMatch = cookie.match(/wr_name=([^;]+)/);
    
    if (vidMatch) {
        userInfo += '<p><strong>用户ID:</strong> ' + vidMatch[1] + '</p>';
    }
    if (nameMatch) {
        userInfo += '<p><strong>用户名:</strong> ' + decodeURIComponent(nameMatch[1]) + '</p>';
    }
    
    content.innerHTML = `
        <h2 style="color: #333; margin-top: 0;">🍪 微信读书Cookie获取成功！</h2>
        ${userInfo}
        <p style="color: #666; margin: 15px 0;">请复制下面的Cookie内容到你的配置文件中：</p>
        <textarea id="cookieText" style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: monospace; font-size: 12px; resize: vertical;" readonly>${cookie}</textarea>
        <div style="margin-top: 15px; text-align: center;">
            <button id="copyBtn" style="background: #07c160; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px; font-size: 14px;">📋 复制Cookie</button>
            <button id="closeBtn" style="background: #666; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 14px;">❌ 关闭</button>
        </div>
        <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 5px; font-size: 12px; color: #666;">
            <strong>💡 使用提示：</strong><br>
            1. 点击"复制Cookie"按钮复制内容<br>
            2. 将Cookie粘贴到项目的.env文件中的WECHAT_READING_COOKIE字段<br>
            3. 保存文件后即可使用微信读书MCP服务器
        </div>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    // 复制功能
    document.getElementById('copyBtn').onclick = function() {
        var textarea = document.getElementById('cookieText');
        textarea.select();
        textarea.setSelectionRange(0, 99999); // 移动端兼容
        
        try {
            document.execCommand('copy');
            this.innerHTML = '✅ 已复制！';
            this.style.background = '#28a745';
            setTimeout(() => {
                this.innerHTML = '📋 复制Cookie';
                this.style.background = '#07c160';
            }, 2000);
        } catch (err) {
            alert('复制失败，请手动选择并复制文本框中的内容');
        }
    };
    
    // 关闭功能
    document.getElementById('closeBtn').onclick = function() {
        document.body.removeChild(modal);
    };
    
    // 点击背景关闭
    modal.onclick = function(e) {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    };
    
    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.body.contains(modal)) {
            document.body.removeChild(modal);
        }
    });
    
})();
