#!/usr/bin/env python3
"""
微信读书MCP服务器安装和设置脚本

整合了install.py的功能，提供完整的安装和配置流程。
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def print_status(message, status="info"):
    """打印状态信息"""
    icons = {"info": "ℹ️", "success": "✅", "error": "❌", "warning": "⚠️"}
    print(f"{icons.get(status, 'ℹ️')} {message}")


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print_status("需要Python 3.8或更高版本", "error")
        return False
    print_status(f"Python版本: {sys.version}", "success")
    return True


def run_command(command):
    """运行命令"""
    print_status(f"执行: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print_status(f"命令执行失败: {result.stderr}", "error")
        return False
    return True


def install_dependencies():
    """安装依赖"""
    print_status("安装依赖包...")
    
    commands = [
        f"{sys.executable} -m pip install --upgrade pip",
        f"{sys.executable} -m pip install -r requirements.txt",
        f"{sys.executable} -m pip install -e ."
    ]
    
    for cmd in commands:
        if not run_command(cmd):
            return False
    
    print_status("依赖安装完成", "success")
    return True


def setup_environment():
    """设置环境"""
    print_status("设置环境...")
    
    # 创建.env文件
    if not os.path.exists(".env"):
        env_content = """# 微信读书认证配置
WECHAT_READING_COOKIE=your_cookie_here

# API请求配置
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/wechat_reading_mcp.log

# MCP服务器配置
MCP_SERVER_NAME=wechat-reading-mcp
MCP_SERVER_VERSION=0.1.0
"""
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)
        print_status("已创建.env文件", "success")
    else:
        print_status(".env文件已存在", "info")
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    print_status("已创建日志目录", "success")


def test_installation():
    """测试安装"""
    print_status("测试安装...")
    
    try:
        # 测试导入
        import wechat_reading_mcp
        print_status("包导入成功", "success")
        
        # 测试配置
        from wechat_reading_mcp.config import Config
        config = Config()
        print_status("配置加载成功", "success")
        
        return True
        
    except Exception as e:
        print_status(f"安装测试失败: {e}", "error")
        return False


def show_next_steps():
    """显示下一步操作"""
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("\n📋 下一步操作：")
    print("1. 配置Cookie: python run.py (选择选项1)")
    print("2. 测试功能: python run.py (选择选项2)")
    print("3. 启动服务器: python run.py (选择选项3)")
    print("4. 或使用快速启动: python run.py --quick")
    print("\n📖 更多信息请查看 README.md")


def main():
    """主函数"""
    print("微信读书MCP服务器安装程序")
    print("=" * 50)
    
    try:
        # 检查Python版本
        if not check_python_version():
            return False
        
        # 安装依赖
        if not install_dependencies():
            return False
        
        # 设置环境
        setup_environment()
        
        # 测试安装
        if not test_installation():
            return False
        
        show_next_steps()
        return True
        
    except KeyboardInterrupt:
        print_status("安装被用户中断", "warning")
        return False
    except Exception as e:
        print_status(f"安装过程中出现错误: {e}", "error")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
