"""
微信读书API测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from wechat_reading_mcp.config import Config
from wechat_reading_mcp.wechat_api import WeChatReadingAPI


class TestWeChatReadingAPI:
    """微信读书API测试类"""
    
    @pytest.fixture
    def config(self):
        """测试配置"""
        return Config(
            wechat_reading_cookie="test_cookie",
            wechat_reading_user_id="123456",
            request_timeout=10,
            max_retries=1,
        )
    
    @pytest.fixture
    def api(self, config):
        """API客户端实例"""
        return WeChatReadingAPI(config)
    
    @pytest.mark.asyncio
    async def test_init(self, api):
        """测试初始化"""
        assert api.config is not None
        assert api.session is None
        assert api._user_id is None
    
    @pytest.mark.asyncio
    async def test_get_user_id_from_config(self, api):
        """测试从配置获取用户ID"""
        user_id = await api._get_user_id()
        assert user_id == "123456"
    
    @pytest.mark.asyncio
    async def test_get_user_id_from_cookie(self):
        """测试从Cookie获取用户ID"""
        config = Config(
            wechat_reading_cookie="wr_vid=789012; other=value",
            request_timeout=10,
        )
        api = WeChatReadingAPI(config)
        
        user_id = await api._get_user_id()
        assert user_id == "789012"
    
    @pytest.mark.asyncio
    async def test_validate_connection_no_auth(self):
        """测试无认证信息时的连接验证"""
        config = Config(wechat_reading_cookie="")
        api = WeChatReadingAPI(config)
        
        with pytest.raises(ValueError, match="未配置微信读书认证信息"):
            await api.validate_connection()
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, api):
        """测试成功的请求"""
        mock_response = Mock()
        mock_response.raise_for_status = Mock()
        mock_response.headers = {"content-type": "application/json"}
        mock_response.json = AsyncMock(return_value={"success": True})
        
        with patch.object(api, '_ensure_session'), \
             patch.object(api, 'session') as mock_session:
            
            mock_session.request = AsyncMock()
            mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_response)
            mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
            
            result = await api._make_request("GET", "/test")
            assert result == {"success": True}
    
    @pytest.mark.asyncio
    async def test_get_book_list(self, api):
        """测试获取书籍列表"""
        mock_response = {
            "books": [
                {
                    "bookId": "123",
                    "title": "测试书籍",
                    "author": "测试作者",
                    "cover": "http://example.com/cover.jpg",
                    "newRating": 4500,
                    "newRatingCount": 100,
                }
            ]
        }
        
        with patch.object(api, '_make_request', return_value=mock_response), \
             patch.object(api, '_get_user_id', return_value="123456"):
            
            books = await api.get_book_list(status="all", limit=10)
            
            assert len(books) == 1
            assert books[0]["book_id"] == "123"
            assert books[0]["title"] == "测试书籍"
            assert books[0]["author"] == "测试作者"
            assert books[0]["rating"] == 4.5
    
    @pytest.mark.asyncio
    async def test_get_book_details(self, api):
        """测试获取书籍详细信息"""
        mock_response = {
            "bookId": "123",
            "title": "测试书籍",
            "author": "测试作者",
            "intro": "这是一本测试书籍",
            "newRating": 4500,
            "totalWords": 100000,
        }
        
        with patch.object(api, '_make_request', return_value=mock_response):
            book_details = await api.get_book_details("123")
            
            assert book_details["book_id"] == "123"
            assert book_details["title"] == "测试书籍"
            assert book_details["rating"] == 4.5
            assert book_details["word_count"] == 100000
    
    @pytest.mark.asyncio
    async def test_get_notes(self, api):
        """测试获取笔记"""
        mock_response = {
            "updated": [
                {
                    "bookmarkId": "note1",
                    "bookId": "123",
                    "bookTitle": "测试书籍",
                    "markText": "这是一条测试笔记",
                    "review": "我的想法",
                    "createTime": 1640995200,
                    "type": 1,
                }
            ]
        }
        
        with patch.object(api, '_make_request', return_value=mock_response), \
             patch.object(api, '_get_user_id', return_value="123456"):
            
            notes = await api.get_notes(book_id="123", limit=10)
            
            assert len(notes) == 1
            assert notes[0]["note_id"] == "note1"
            assert notes[0]["book_id"] == "123"
            assert notes[0]["content"] == "这是一条测试笔记"
            assert notes[0]["note"] == "我的想法"
    
    @pytest.mark.asyncio
    async def test_search_books(self, api):
        """测试搜索书籍"""
        mock_response = {
            "books": [
                {
                    "bookId": "456",
                    "title": "搜索结果书籍",
                    "author": "搜索作者",
                    "newRating": 4000,
                }
            ]
        }
        
        with patch.object(api, '_make_request', return_value=mock_response):
            books = await api.search_books(query="测试", limit=10)
            
            assert len(books) == 1
            assert books[0]["book_id"] == "456"
            assert books[0]["title"] == "搜索结果书籍"
            assert books[0]["rating"] == 4.0
    
    @pytest.mark.asyncio
    async def test_close_session(self, api):
        """测试关闭会话"""
        # 创建一个模拟的会话
        mock_session = Mock()
        mock_session.closed = False
        mock_session.close = AsyncMock()
        api.session = mock_session
        
        await api.close()
        mock_session.close.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
