#!/usr/bin/env python3
"""
微信读书MCP服务器安装脚本

自动化安装和配置微信读书MCP服务器。
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, check=True):
    """运行命令"""
    print(f"执行: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"命令执行失败: {command}")
        print(f"错误输出: {result.stderr}")
        sys.exit(1)
    
    return result


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"Python版本: {sys.version}")


def install_dependencies():
    """安装依赖"""
    print("\n安装依赖包...")
    
    # 升级pip
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # 安装项目依赖
    if os.path.exists("requirements.txt"):
        run_command(f"{sys.executable} -m pip install -r requirements.txt")
    else:
        # 如果没有requirements.txt，安装基本依赖
        dependencies = [
            "mcp>=1.0.0",
            "requests>=2.31.0",
            "aiohttp>=3.9.0",
            "pydantic>=2.5.0",
            "python-dotenv>=1.0.0",
            "loguru>=0.7.0",
            "python-dateutil>=2.8.0",
            "cryptography>=41.0.0",
        ]
        
        for dep in dependencies:
            run_command(f"{sys.executable} -m pip install {dep}")


def setup_environment():
    """设置环境"""
    print("\n设置环境...")
    
    # 创建.env文件
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("已创建.env文件，请编辑并填入你的微信读书Cookie")
        else:
            # 创建基本的.env文件
            env_content = """# 微信读书认证配置
WECHAT_READING_COOKIE=your_cookie_here

# API请求配置
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/wechat_reading_mcp.log

# MCP服务器配置
MCP_SERVER_NAME=wechat-reading-mcp
MCP_SERVER_VERSION=0.1.0
"""
            with open(".env", "w", encoding="utf-8") as f:
                f.write(env_content)
            print("已创建.env文件，请编辑并填入你的微信读书Cookie")
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    print("已创建日志目录")


def install_package():
    """安装包"""
    print("\n安装微信读书MCP服务器...")
    
    # 以开发模式安装
    run_command(f"{sys.executable} -m pip install -e .")


def test_installation():
    """测试安装"""
    print("\n测试安装...")
    
    try:
        # 测试导入
        import wechat_reading_mcp
        print("✓ 包导入成功")
        
        # 测试配置
        from wechat_reading_mcp.config import Config
        config = Config()
        print("✓ 配置加载成功")
        
        print("安装测试通过!")
        
    except Exception as e:
        print(f"✗ 安装测试失败: {e}")
        return False
    
    return True


def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*50)
    print("安装完成!")
    print("="*50)
    
    print("\n后续步骤:")
    print("1. 编辑 .env 文件，填入你的微信读书Cookie")
    print("   - 访问 https://weread.qq.com/")
    print("   - 登录后打开开发者工具，复制Cookie")
    print("   - 将Cookie填入 .env 文件中的 WECHAT_READING_COOKIE")
    
    print("\n2. 测试服务器:")
    print("   python examples/demo.py")
    
    print("\n3. 运行MCP服务器:")
    print("   python -m wechat_reading_mcp.server")
    
    print("\n4. 在Claude Desktop中配置:")
    print("   参考 claude_desktop_config.json 文件")
    
    print("\n5. 查看文档:")
    print("   README.md")


def main():
    """主函数"""
    print("微信读书MCP服务器安装程序")
    print("="*50)
    
    try:
        # 检查Python版本
        check_python_version()
        
        # 安装依赖
        install_dependencies()
        
        # 设置环境
        setup_environment()
        
        # 安装包
        install_package()
        
        # 测试安装
        if test_installation():
            show_next_steps()
        else:
            print("安装过程中出现问题，请检查错误信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
