#!/usr/bin/env python3
"""
微信读书MCP服务器管理脚本

提供完整的命令行界面来管理MCP服务器，包括配置、测试、启动等功能。
"""

import os
import sys
import subprocess


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("📚 微信读书MCP服务器管理工具")
    print("=" * 60)


def print_status(message, status="info"):
    """打印状态信息"""
    icons = {"info": "ℹ️", "success": "✅", "error": "❌", "warning": "⚠️"}
    print(f"{icons.get(status, 'ℹ️')} {message}")


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print_status("需要Python 3.8或更高版本", "error")
        return False
    return True


def check_environment():
    """检查环境配置"""
    print_status("检查环境配置...")

    # 检查.env文件
    if not os.path.exists(".env"):
        print_status("未找到.env文件", "error")
        print_status("请先配置Cookie", "info")
        return False

    # 检查Cookie配置
    with open(".env", "r", encoding="utf-8") as f:
        env_content = f.read()

    if "WECHAT_READING_COOKIE=your_cookie_here" in env_content or "WECHAT_READING_COOKIE=" not in env_content:
        print_status("未配置微信读书Cookie", "error")
        print_status("请先配置Cookie", "info")
        return False

    print_status("环境配置检查通过", "success")
    return True


def check_and_create_env():
    """检查并创建.env文件"""
    if os.path.exists(".env"):
        # 检查Cookie是否已配置
        with open(".env", "r", encoding="utf-8") as f:
            content = f.read()

        if "WECHAT_READING_COOKIE=your_cookie_here" in content:
            print_status("请先配置微信读书Cookie", "warning")
            return False
        elif "WECHAT_READING_COOKIE=" not in content:
            print_status("请先配置微信读书Cookie", "warning")
            return False

        print_status("环境配置已就绪", "success")
        return True
    else:
        print_status("创建.env配置文件...", "info")
        env_content = """# 微信读书认证配置
WECHAT_READING_COOKIE=your_cookie_here

# API请求配置
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/wechat_reading_mcp.log

# MCP服务器配置
MCP_SERVER_NAME=wechat-reading-mcp
MCP_SERVER_VERSION=0.1.0
"""
        with open(".env", "w", encoding="utf-8") as f:
            f.write(env_content)

        print_status("已创建.env文件，请配置Cookie", "warning")
        return False


def install_dependencies():
    """安装依赖"""
    print_status("检查并安装依赖...")

    try:
        # 检查是否已安装
        import wechat_reading_mcp
        print_status("依赖已安装", "success")
        return True
    except ImportError:
        pass

    # 安装依赖
    commands = [
        f"{sys.executable} -m pip install --upgrade pip",
        f"{sys.executable} -m pip install -r requirements.txt",
        f"{sys.executable} -m pip install -e ."
    ]

    for cmd in commands:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print_status(f"安装失败: {result.stderr}", "error")
            return False

    print_status("依赖安装完成", "success")
    return True


def run_command(command, description=""):
    """运行命令"""
    if description:
        print_status(description)

    print(f"执行: {command}")
    result = subprocess.run(command, shell=True)
    return result.returncode == 0


def quick_start():
    """快速启动模式"""
    print_status("快速启动模式")

    # 检查Python版本
    if not check_python_version():
        return False

    # 检查并创建环境
    if not check_and_create_env():
        print("\n📋 获取Cookie的方法：")
        print("1. 运行: python tools/cookie_helper.py")
        print("2. 或访问: https://weread.qq.com/ 登录后使用浏览器书签工具")
        print("3. 或查看: docs/获取Cookie指南.md")
        return False

    # 安装依赖
    if not install_dependencies():
        return False

    # 启动服务器
    print_status("启动MCP服务器...")
    print_status("服务器启动后，可以在Claude Desktop中使用", "info")
    print_status("按Ctrl+C停止服务器", "info")
    print()

    try:
        subprocess.run([sys.executable, "-m", "wechat_reading_mcp.server"])
    except KeyboardInterrupt:
        print()
        print_status("服务器已停止", "info")
    except Exception as e:
        print_status(f"服务器启动失败: {e}", "error")
        return False

    return True


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ["--quick", "-q", "quick"]:
            return quick_start()
        elif sys.argv[1] in ["--help", "-h", "help"]:
            print_help()
            return

    print_banner()

    while True:
        print("\n" + "=" * 60)
        print("请选择操作：")
        print("0. ⚡ 快速启动 (一键配置并启动)")
        print("1. 🍪 配置Cookie")
        print("2. 🧪 运行功能演示")
        print("3. 🚀 启动MCP服务器")
        print("4. 🔧 安装/更新依赖")
        print("5. 🧹 运行测试")
        print("6. 📋 生成Claude配置")
        print("7. 📖 查看文档")
        print("8. ❌ 退出")

        choice = input("\n请输入选择 (0-8): ").strip()

        if choice == "0":
            print("\n⚡ 快速启动模式...")
            quick_start()

        elif choice == "1":
            print("\n🍪 启动Cookie配置助手...")
            run_command("python tools/cookie_helper.py", "配置微信读书Cookie")

        elif choice == "2":
            if not check_environment():
                continue

            print("\n🧪 运行功能演示...")
            run_command("python examples/demo.py", "演示微信读书MCP功能")

        elif choice == "3":
            if not check_environment():
                continue

            print("\n🚀 启动MCP服务器...")
            print_status("服务器启动后，可以在Claude Desktop中使用", "info")
            print_status("按Ctrl+C停止服务器", "info")
            try:
                subprocess.run([sys.executable, "-m", "wechat_reading_mcp.server"])
            except KeyboardInterrupt:
                print()
                print_status("服务器已停止", "info")

        elif choice == "4":
            print("\n🔧 安装/更新依赖...")
            if os.path.exists("scripts/setup.py"):
                run_command("python scripts/setup.py", "安装项目依赖")
            else:
                if install_dependencies():
                    print_status("依赖安装完成", "success")
                else:
                    print_status("依赖安装失败", "error")

        elif choice == "5":
            print("\n🧹 运行测试...")
            if os.path.exists("scripts/test.py"):
                run_command("python scripts/test.py", "运行完整测试套件")
            elif os.path.exists("tests"):
                run_command("python -m pytest tests/ -v", "运行单元测试")
            else:
                print_status("未找到测试文件", "error")

        elif choice == "6":
            print("\n📋 生成Claude Desktop配置...")
            run_command("python tools/claude_config_generator.py", "生成Claude Desktop配置")

        elif choice == "7":
            print("\n📖 可用文档：")
            docs = [
                ("README.md", "项目主要文档和快速开始指南"),
                ("docs/获取Cookie指南.md", "Cookie获取详细指南"),
                ("scripts/", "管理脚本目录"),
                ("tools/", "工具脚本目录"),
                ("examples/", "示例代码目录"),
            ]

            for doc, desc in docs:
                if os.path.exists(doc):
                    print(f"   📄 {doc} - {desc}")

            print("\n💡 建议先阅读README.md了解项目概况")
            print("💡 使用 python run.py --help 查看命令行选项")

        elif choice == "8":
            print("\n👋 再见！")
            break

        else:
            print_status("无效选择，请输入0-8", "error")


def print_help():
    """打印帮助信息"""
    print("微信读书MCP服务器管理工具")
    print("\n用法:")
    print("  python run.py           # 交互式菜单")
    print("  python run.py --quick   # 快速启动模式")
    print("  python run.py --help    # 显示帮助")
    print("\n快速启动模式会自动:")
    print("  1. 检查Python版本")
    print("  2. 创建配置文件")
    print("  3. 安装依赖")
    print("  4. 启动MCP服务器")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出现错误: {e}")
        import traceback
        traceback.print_exc()
