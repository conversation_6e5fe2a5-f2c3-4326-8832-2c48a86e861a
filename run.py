#!/usr/bin/env python3
"""
微信读书MCP服务器一键运行脚本

提供简单的命令行界面来管理MCP服务器。
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("📚 微信读书MCP服务器")
    print("=" * 60)


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查.env文件
    if not os.path.exists(".env"):
        print("❌ 未找到.env文件")
        print("💡 请先运行: python tools/cookie_helper.py 配置Cookie")
        return False
    
    # 检查Cookie配置
    with open(".env", "r", encoding="utf-8") as f:
        env_content = f.read()
    
    if "WECHAT_READING_COOKIE=your_cookie_here" in env_content or "WECHAT_READING_COOKIE=" not in env_content:
        print("❌ 未配置微信读书Cookie")
        print("💡 请先运行: python tools/cookie_helper.py 配置Cookie")
        return False
    
    print("✅ 环境配置检查通过")
    return True


def run_command(command, description=""):
    """运行命令"""
    if description:
        print(f"\n🚀 {description}")
    
    print(f"执行: {command}")
    result = subprocess.run(command, shell=True)
    return result.returncode == 0


def main():
    """主函数"""
    print_banner()
    
    while True:
        print("\n" + "=" * 60)
        print("请选择操作：")
        print("1. 🍪 配置Cookie")
        print("2. 🧪 运行功能演示")
        print("3. 🚀 启动MCP服务器")
        print("4. 🔧 安装/更新依赖")
        print("5. 🧹 运行测试")
        print("6. 📖 查看文档")
        print("7. ❌ 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice == "1":
            print("\n🍪 启动Cookie配置助手...")
            run_command("python tools/cookie_helper.py", "配置微信读书Cookie")
        
        elif choice == "2":
            if not check_environment():
                continue
            
            print("\n🧪 运行功能演示...")
            run_command("python examples/demo.py", "演示微信读书MCP功能")
        
        elif choice == "3":
            if not check_environment():
                continue
            
            print("\n🚀 启动MCP服务器...")
            print("💡 服务器启动后，可以在Claude Desktop中使用")
            print("💡 按Ctrl+C停止服务器")
            run_command("python -m wechat_reading_mcp.server", "启动MCP服务器")
        
        elif choice == "4":
            print("\n🔧 安装/更新依赖...")
            success = run_command("python install.py", "安装项目依赖")
            if success:
                print("✅ 依赖安装完成")
            else:
                print("❌ 依赖安装失败")
        
        elif choice == "5":
            print("\n🧹 运行测试...")
            if os.path.exists("tests"):
                run_command("python -m pytest tests/ -v", "运行单元测试")
            else:
                print("❌ 未找到测试目录")
        
        elif choice == "6":
            print("\n📖 可用文档：")
            docs = [
                ("README.md", "项目主要文档"),
                ("docs/获取Cookie指南.md", "Cookie获取详细指南"),
                ("claude_desktop_config.json", "Claude Desktop配置示例"),
                (".env.example", "环境变量配置示例"),
            ]
            
            for doc, desc in docs:
                if os.path.exists(doc):
                    print(f"   📄 {doc} - {desc}")
            
            print("\n💡 建议先阅读README.md了解项目概况")
        
        elif choice == "7":
            print("\n👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请输入1-7")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出现错误: {e}")
        import traceback
        traceback.print_exc()
