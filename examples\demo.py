#!/usr/bin/env python3
"""
微信读书MCP服务器演示脚本

展示如何使用微信读书API客户端的各种功能。
"""

import asyncio
import json
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from wechat_reading_mcp.config import Config
from wechat_reading_mcp.wechat_api import WeChatReadingAPI
from dotenv import load_dotenv


async def demo_get_book_list(api: WeChatReadingAPI):
    """演示获取书籍列表"""
    print("\n=== 获取书籍列表 ===")
    
    try:
        # 获取在读书籍
        reading_books = await api.get_book_list(status="reading", limit=5)
        print(f"在读书籍 ({len(reading_books)} 本):")
        for book in reading_books:
            print(f"  - {book['title']} by {book['author']}")
        
        # 获取已读书籍
        finished_books = await api.get_book_list(status="finished", limit=5)
        print(f"\n已读书籍 ({len(finished_books)} 本):")
        for book in finished_books:
            print(f"  - {book['title']} by {book['author']} (评分: {book['rating']})")
        
    except Exception as e:
        print(f"获取书籍列表失败: {e}")


async def demo_get_book_details(api: WeChatReadingAPI):
    """演示获取书籍详细信息"""
    print("\n=== 获取书籍详细信息 ===")
    
    try:
        # 先获取一本书的ID
        books = await api.get_book_list(status="all", limit=1)
        if not books:
            print("没有找到书籍")
            return
        
        book_id = books[0]["book_id"]
        print(f"获取书籍详细信息: {books[0]['title']}")
        
        details = await api.get_book_details(book_id)
        print(f"书名: {details['title']}")
        print(f"作者: {details['author']}")
        print(f"简介: {details['intro'][:100]}...")
        print(f"评分: {details['rating']} ({details['rating_count']} 人评价)")
        print(f"字数: {details['word_count']:,} 字")
        print(f"出版社: {details['publisher']}")
        
        if "reading_progress" in details:
            print(f"阅读进度: {details['reading_progress']}%")
        
    except Exception as e:
        print(f"获取书籍详细信息失败: {e}")


async def demo_get_notes(api: WeChatReadingAPI):
    """演示获取笔记和划线"""
    print("\n=== 获取笔记和划线 ===")
    
    try:
        # 获取最近的笔记
        notes = await api.get_notes(limit=5)
        print(f"最近的笔记 ({len(notes)} 条):")
        
        for note in notes:
            print(f"\n书籍: {note['book_title']}")
            print(f"章节: {note['chapter_title']}")
            print(f"内容: {note['content'][:50]}...")
            if note['note']:
                print(f"笔记: {note['note'][:50]}...")
            print(f"时间: {note['create_time']}")
        
    except Exception as e:
        print(f"获取笔记失败: {e}")


async def demo_search_books(api: WeChatReadingAPI):
    """演示搜索书籍"""
    print("\n=== 搜索书籍 ===")
    
    search_queries = ["三体", "Python", "人工智能"]
    
    for query in search_queries:
        try:
            print(f"\n搜索: {query}")
            books = await api.search_books(query=query, limit=3)
            
            if books:
                for book in books:
                    print(f"  - {book['title']} by {book['author']}")
                    if book['rating'] > 0:
                        print(f"    评分: {book['rating']} ({book['rating_count']} 人评价)")
            else:
                print(f"  没有找到相关书籍")
                
        except Exception as e:
            print(f"搜索 '{query}' 失败: {e}")


async def main():
    """主函数"""
    print("微信读书MCP服务器演示")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 创建配置和API客户端
    try:
        config = Config()
        if not config.is_authenticated:
            print("错误: 未配置微信读书认证信息")
            print("请在 .env 文件中设置 WECHAT_READING_COOKIE")
            return
        
        async with WeChatReadingAPI(config) as api:
            # 验证连接
            print("验证微信读书API连接...")
            await api.validate_connection()
            print("连接验证成功!")
            
            # 演示各种功能
            await demo_get_book_list(api)
            await demo_get_book_details(api)
            await demo_get_notes(api)
            await demo_search_books(api)
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n演示完成!")


if __name__ == "__main__":
    asyncio.run(main())
