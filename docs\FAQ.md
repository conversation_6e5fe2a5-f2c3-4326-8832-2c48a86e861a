# 常见问题解答 (FAQ)

## 🔧 安装和配置

### Q: 支持哪些Python版本？
A: 本项目需要Python 3.8或更高版本。推荐使用Python 3.9+以获得最佳性能。

### Q: 可以在哪些操作系统上运行？
A: 支持Windows、macOS和Linux系统。

### Q: 安装过程中出现依赖错误怎么办？
A: 
1. 确保Python版本符合要求
2. 升级pip: `python -m pip install --upgrade pip`
3. 使用虚拟环境: `python -m venv venv && source venv/bin/activate`
4. 重新运行安装: `python run.py --quick`

## 🍪 Cookie相关

### Q: Cookie多久会过期？
A: 微信读书的Cookie通常有效期为几天到几周不等，具体取决于账号活跃度和安全设置。

### Q: Cookie过期了怎么办？
A: 重新获取Cookie并更新配置：
```bash
python tools/cookie_helper.py
```

### Q: 如何验证Cookie是否有效？
A: 运行测试命令：
```bash
python scripts/test.py
```

### Q: Cookie获取失败怎么办？
A: 
1. 确保已登录微信读书网页版
2. 清除浏览器缓存后重新登录
3. 尝试使用无痕模式
4. 检查网络连接是否正常

## 🤖 Claude Desktop集成

### Q: 配置Claude Desktop后看不到工具怎么办？
A: 
1. 确保配置文件格式正确
2. 重启Claude Desktop
3. 检查MCP服务器是否正在运行
4. 查看Claude Desktop的错误日志

### Q: 如何确认MCP服务器正在运行？
A: 在终端中运行：
```bash
python -m wechat_reading_mcp.server
```
如果看到启动信息且没有错误，说明服务器正常运行。

### Q: Claude Desktop配置文件在哪里？
A: 
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

## 🔍 功能使用

### Q: 为什么获取不到书籍数据？
A: 可能的原因：
1. Cookie已过期，需要重新获取
2. 账号中没有相应的数据
3. 网络连接问题
4. 微信读书API临时不可用

### Q: 搜索功能返回结果为空？
A: 
1. 检查搜索关键词是否正确
2. 尝试使用更通用的关键词
3. 确认网络连接正常
4. 检查Cookie是否有效

### Q: 笔记数据不完整怎么办？
A: 
1. 确认在微信读书中确实有笔记
2. 尝试增加limit参数获取更多数据
3. 检查特定书籍的笔记: 指定book_id参数

## 🐛 错误排除

### Q: 出现"认证失败"错误？
A: 
1. 重新获取Cookie
2. 检查.env文件中的Cookie配置
3. 确认Cookie格式正确（完整的Cookie字符串）

### Q: 出现"网络连接错误"？
A: 
1. 检查网络连接
2. 确认可以访问weread.qq.com
3. 检查防火墙设置
4. 尝试使用VPN（如果在网络受限环境）

### Q: 服务器启动失败？
A: 
1. 检查Python版本和依赖
2. 查看错误日志: `cat logs/wechat_reading_mcp.log`
3. 重新安装依赖: `python scripts/setup.py`
4. 检查端口是否被占用

## 📊 性能优化

### Q: 如何提高响应速度？
A: 
1. 减少limit参数值
2. 使用更具体的搜索关键词
3. 定期清理日志文件
4. 确保网络连接稳定

### Q: 如何减少内存使用？
A: 
1. 适当设置limit参数
2. 定期重启MCP服务器
3. 清理临时文件和缓存

## 🔒 安全相关

### Q: Cookie信息安全吗？
A: 
1. Cookie仅存储在本地
2. 不会上传到任何第三方服务器
3. 建议定期更新Cookie
4. 不要在公共环境使用

### Q: 如何保护隐私？
A: 
1. 不要分享Cookie给他人
2. 在安全的网络环境下使用
3. 定期检查和更新配置
4. 使用完毕后可以删除Cookie

## 🛠️ 开发相关

### Q: 如何贡献代码？
A: 
1. Fork项目仓库
2. 创建功能分支
3. 编写测试用例
4. 提交Pull Request

### Q: 如何添加新功能？
A: 
1. 查看现有API文档
2. 在tools.py中添加新工具
3. 更新测试用例
4. 更新文档

### Q: 如何调试问题？
A: 
1. 启用DEBUG日志级别
2. 查看详细错误信息
3. 使用测试脚本验证功能
4. 检查网络请求和响应

## 📞 获取帮助

如果以上FAQ没有解决你的问题，可以：

1. 📖 查看详细文档: [README.md](../README.md)
2. 🔍 搜索已有问题: [GitHub Issues](https://github.com/your-username/wechat-reading-mcp/issues)
3. 💬 提交新问题: [新建Issue](https://github.com/your-username/wechat-reading-mcp/issues/new)
4. 📧 联系维护者

---

**提示**: 在提交问题时，请提供详细的错误信息、操作步骤和系统环境，这样能帮助我们更快地解决问题。
