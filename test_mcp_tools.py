#!/usr/bin/env python3
"""
测试MCP工具功能
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from wechat_reading_mcp.config import Config
from wechat_reading_mcp.wechat_api import WeChatReadingAPI
from wechat_reading_mcp.tools import search_books_tool, get_book_list_tool


async def test_search_tool():
    """测试搜索工具"""
    print("=== 测试搜索工具 ===")
    
    config = Config()
    api = WeChatReadingAPI(config)
    
    # 测试搜索工具
    arguments = {"query": "三体", "limit": 2}
    result = await search_books_tool(api, arguments)
    
    print(f"搜索工具结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    await api.close()


async def test_book_list_tool():
    """测试书籍列表工具"""
    print("\n=== 测试书籍列表工具 ===")
    
    config = Config()
    api = WeChatReadingAPI(config)
    
    # 测试书籍列表工具
    arguments = {"status": "all", "limit": 5}
    result = await get_book_list_tool(api, arguments)
    
    print(f"书籍列表工具结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    await api.close()


async def main():
    """主函数"""
    await test_search_tool()
    await test_book_list_tool()


if __name__ == "__main__":
    asyncio.run(main())
