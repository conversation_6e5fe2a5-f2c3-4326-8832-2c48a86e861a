#!/usr/bin/env python3
"""
测试微信读书MCP服务器

验证MCP服务器的基本功能是否正常工作。
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path


async def test_mcp_server():
    """测试MCP服务器"""
    print("🧪 测试微信读书MCP服务器")
    print("=" * 50)
    
    # 启动MCP服务器进程
    print("🚀 启动MCP服务器...")
    
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, "-m", "wechat_reading_mcp.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
        if process.poll() is not None:
            # 进程已退出，读取错误信息
            stdout, stderr = process.communicate()
            print("❌ 服务器启动失败")
            print(f"错误信息: {stderr}")
            return False
        
        print("✅ 服务器启动成功")
        
        # 测试工具列表请求
        print("\n📋 测试工具列表请求...")
        
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        
        # 发送请求
        request_json = json.dumps(list_tools_request) + "\n"
        process.stdin.write(request_json)
        process.stdin.flush()
        
        # 读取响应
        response_line = process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line)
                if "result" in response and "tools" in response["result"]:
                    tools = response["result"]["tools"]
                    print(f"✅ 成功获取 {len(tools)} 个工具:")
                    for tool in tools:
                        print(f"   - {tool['name']}: {tool['description']}")
                else:
                    print("❌ 工具列表响应格式不正确")
                    print(f"响应: {response}")
            except json.JSONDecodeError as e:
                print(f"❌ 响应JSON解析失败: {e}")
                print(f"原始响应: {response_line}")
        else:
            print("❌ 未收到响应")
        
        # 测试工具调用
        print("\n🔧 测试工具调用...")
        
        call_tool_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "get_book_list",
                "arguments": {
                    "status": "all",
                    "limit": 5
                }
            }
        }
        
        # 发送请求
        request_json = json.dumps(call_tool_request) + "\n"
        process.stdin.write(request_json)
        process.stdin.flush()
        
        # 读取响应
        response_line = process.stdout.readline()
        if response_line:
            try:
                response = json.loads(response_line)
                if "result" in response:
                    print("✅ 工具调用成功")
                    # 解析结果
                    if "content" in response["result"]:
                        content = response["result"]["content"]
                        if content and len(content) > 0:
                            result_text = content[0].get("text", "")
                            try:
                                result_data = json.loads(result_text)
                                if result_data.get("success"):
                                    print(f"✅ 获取书籍列表成功: {result_data.get('message', '')}")
                                else:
                                    print(f"⚠️  API调用失败: {result_data.get('error', '')}")
                            except json.JSONDecodeError:
                                print(f"📄 响应内容: {result_text[:200]}...")
                else:
                    print("❌ 工具调用响应格式不正确")
                    print(f"响应: {response}")
            except json.JSONDecodeError as e:
                print(f"❌ 响应JSON解析失败: {e}")
                print(f"原始响应: {response_line}")
        else:
            print("❌ 未收到响应")
        
        print("\n✅ MCP服务器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False
        
    finally:
        # 清理进程
        if 'process' in locals():
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            except Exception:
                pass


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查.env文件
    if not Path(".env").exists():
        print("❌ 未找到.env文件")
        print("💡 请先运行: python tools/cookie_helper.py 配置Cookie")
        return False
    
    # 检查Cookie配置
    with open(".env", "r", encoding="utf-8") as f:
        env_content = f.read()
    
    if "WECHAT_READING_COOKIE=" not in env_content:
        print("❌ 未找到Cookie配置")
        return False
    
    if "your_cookie_here" in env_content:
        print("❌ 请配置真实的Cookie")
        print("💡 请先运行: python tools/cookie_helper.py 配置Cookie")
        return False
    
    print("✅ 环境配置检查通过")
    return True


async def main():
    """主函数"""
    print("🧪 微信读书MCP服务器测试工具")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        return
    
    # 测试服务器
    success = await test_mcp_server()
    
    if success:
        print("\n🎉 所有测试通过！")
        print("\n📋 下一步:")
        print("1. 在Claude Desktop中配置MCP服务器")
        print("2. 重启Claude Desktop")
        print("3. 开始使用微信读书功能")
        print("\n💡 配置方法:")
        print("   python tools/claude_config_generator.py")
    else:
        print("\n❌ 测试失败，请检查错误信息")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
