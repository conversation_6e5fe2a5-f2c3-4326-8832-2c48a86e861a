"""
MCP工具函数模块

实现各种微信读书功能的工具函数。
"""

from typing import Any, Dict, List, Optional
from loguru import logger

from .wechat_api import WeChatReadingAPI


async def get_book_list_tool(api: WeChatReadingAPI, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """获取书籍列表工具"""
    status = arguments.get("status", "all")
    limit = arguments.get("limit", 20)
    
    logger.info(f"获取书籍列表: status={status}, limit={limit}")
    
    try:
        books = await api.get_book_list(status=status, limit=limit)
        
        result = {
            "success": True,
            "data": {
                "books": books,
                "total": len(books),
                "status": status,
                "limit": limit
            },
            "message": f"成功获取 {len(books)} 本书籍"
        }
        
        return result
        
    except Exception as e:
        logger.error(f"获取书籍列表失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "获取书籍列表失败"
        }


async def get_book_details_tool(api: WeChatReadingAPI, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """获取书籍详细信息工具"""
    book_id = arguments.get("book_id")
    
    if not book_id:
        return {
            "success": False,
            "error": "缺少必需参数: book_id",
            "message": "请提供书籍ID"
        }
    
    logger.info(f"获取书籍详细信息: book_id={book_id}")
    
    try:
        book_details = await api.get_book_details(book_id)
        
        result = {
            "success": True,
            "data": book_details,
            "message": f"成功获取书籍 {book_id} 的详细信息"
        }
        
        return result
        
    except Exception as e:
        logger.error(f"获取书籍详细信息失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"获取书籍 {book_id} 详细信息失败"
        }


async def get_notes_tool(api: WeChatReadingAPI, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """获取笔记和划线内容工具"""
    book_id = arguments.get("book_id")
    limit = arguments.get("limit", 50)
    
    logger.info(f"获取笔记: book_id={book_id}, limit={limit}")
    
    try:
        notes = await api.get_notes(book_id=book_id, limit=limit)
        
        result = {
            "success": True,
            "data": {
                "notes": notes,
                "total": len(notes),
                "book_id": book_id,
                "limit": limit
            },
            "message": f"成功获取 {len(notes)} 条笔记"
        }
        
        return result
        
    except Exception as e:
        logger.error(f"获取笔记失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "获取笔记失败"
        }


async def search_books_tool(api: WeChatReadingAPI, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """搜索书籍工具"""
    query = arguments.get("query")
    limit = arguments.get("limit", 20)
    
    if not query:
        return {
            "success": False,
            "error": "缺少必需参数: query",
            "message": "请提供搜索关键词"
        }
    
    logger.info(f"搜索书籍: query={query}, limit={limit}")
    
    try:
        books = await api.search_books(query=query, limit=limit)
        
        result = {
            "success": True,
            "data": {
                "books": books,
                "total": len(books),
                "query": query,
                "limit": limit
            },
            "message": f"搜索到 {len(books)} 本相关书籍"
        }
        
        return result
        
    except Exception as e:
        logger.error(f"搜索书籍失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"搜索书籍失败: {query}"
        }
