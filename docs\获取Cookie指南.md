# 微信读书Cookie获取详细指南

## 方法一：通过Chrome浏览器获取（推荐）

### 步骤1：打开微信读书网页版
1. 打开Chrome浏览器
2. 访问：https://weread.qq.com/
3. 使用微信扫码登录你的微信读书账号

### 步骤2：打开开发者工具
1. 在网页上右键点击，选择"检查"（或按F12键）
2. 开发者工具会在页面底部或右侧打开

### 步骤3：切换到Network标签
1. 在开发者工具中点击"Network"（网络）标签
2. 如果看不到Network标签，可能需要点击">>"展开更多标签

### 步骤4：刷新页面并查找请求
1. 按F5刷新页面（或点击浏览器刷新按钮）
2. 在Network标签中会出现很多网络请求
3. 在左侧的请求列表中，找到任意一个以"weread.qq.com"开头的请求
4. 点击该请求

### 步骤5：复制Cookie
1. 在右侧的详情面板中，点击"Headers"（请求头）标签
2. 向下滚动找到"Request Headers"（请求头）部分
3. 找到"Cookie:"这一行
4. 复制Cookie后面的完整内容（通常很长，包含多个键值对）

### Cookie示例格式
```
wr_name=your_name; wr_avatar=avatar_url; wr_vid=123456789; wr_skey=your_skey; ...
```

## 方法二：通过Firefox浏览器获取

### 步骤1-3：同Chrome方法
1. 访问 https://weread.qq.com/ 并登录
2. 按F12打开开发者工具
3. 点击"网络"标签

### 步骤4-5：获取Cookie
1. 刷新页面
2. 在网络请求列表中点击任意一个weread.qq.com的请求
3. 在右侧面板中找到"请求头"
4. 复制Cookie值

## 方法三：通过浏览器地址栏（简单方法）

### 对于Chrome用户：
1. 登录微信读书后，在地址栏左侧点击锁形图标
2. 点击"Cookie"
3. 点击"weread.qq.com"
4. 手动复制所有Cookie的名称和值，格式为：name1=value1; name2=value2; ...

## 常见问题解决

### 问题1：找不到Network标签
**解决方案：**
- 确保开发者工具窗口足够宽
- 点击开发者工具右上角的">>"按钮展开更多标签
- 或者点击开发者工具右上角的设置按钮，确保Network面板已启用

### 问题2：Network中没有请求
**解决方案：**
- 确保已经刷新了页面
- 清除浏览器缓存后重新访问
- 确保网络连接正常

### 问题3：找不到Cookie字段
**解决方案：**
- 确保点击的是weread.qq.com域名下的请求
- 在Request Headers中仔细查找，Cookie通常在较靠前的位置
- 如果没有Cookie，可能需要重新登录

### 问题4：Cookie太长无法完整复制
**解决方案：**
- 在Cookie行上右键，选择"复制值"
- 或者双击Cookie值，然后Ctrl+A全选，Ctrl+C复制

## Cookie有效性验证

获取Cookie后，可以通过以下方式验证：

### 方法1：使用curl命令测试
```bash
curl -H "Cookie: 你的cookie内容" https://weread.qq.com/web/shelf
```

### 方法2：运行项目的演示脚本
```bash
# 将Cookie填入.env文件后运行
python examples/demo.py
```

## 安全注意事项

1. **保密性**：Cookie包含你的登录凭证，不要分享给他人
2. **有效期**：Cookie有时效性，过期后需要重新获取
3. **存储安全**：将Cookie存储在.env文件中，不要提交到代码仓库

## 常见Cookie字段说明

- `wr_vid`：用户ID
- `wr_skey`：会话密钥
- `wr_name`：用户名
- `wr_avatar`：头像URL

## 如果仍然无法获取Cookie

如果按照上述步骤仍然无法获取Cookie，请：

1. 确保已经成功登录微信读书网页版
2. 尝试使用无痕/隐私模式重新登录
3. 清除浏览器缓存和Cookie后重新登录
4. 尝试使用不同的浏览器

如果问题依然存在，可以提供具体的错误信息或截图，我会进一步协助解决。
