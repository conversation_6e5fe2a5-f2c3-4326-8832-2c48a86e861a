#!/usr/bin/env python3
"""
微信读书MCP服务器测试脚本

整合了所有测试功能，包括API测试、MCP服务器测试等。
"""

import asyncio
import json
import subprocess
import sys
import time
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def print_status(message, status="info"):
    """打印状态信息"""
    icons = {"info": "ℹ️", "success": "✅", "error": "❌", "warning": "⚠️"}
    print(f"{icons.get(status, 'ℹ️')} {message}")


async def test_api_functions():
    """测试API功能"""
    print_status("测试微信读书API功能")
    print("=" * 50)
    
    try:
        from wechat_reading_mcp.config import Config
        from wechat_reading_mcp.wechat_api import WeChatReadingAPI
        
        config = Config()
        if not config.wechat_reading_cookie or config.wechat_reading_cookie == "your_cookie_here":
            print_status("请先配置微信读书Cookie", "error")
            return False
        
        async with WeChatReadingAPI(config) as api:
            # 测试获取书籍列表
            print_status("测试获取书籍列表...")
            books = await api.get_book_list(status="all", limit=3)
            print_status(f"获取到 {len(books)} 本书籍", "success")
            
            if books:
                # 测试获取书籍详情
                print_status("测试获取书籍详情...")
                book_details = await api.get_book_details(books[0]['book_id'])
                print_status(f"获取书籍详情: {book_details['title']}", "success")
            
            # 测试获取笔记
            print_status("测试获取笔记...")
            notes = await api.get_notes(limit=3)
            print_status(f"获取到 {len(notes)} 条笔记", "success")
            
            # 测试搜索
            print_status("测试搜索功能...")
            search_results = await api.search_books("三体", limit=2)
            print_status(f"搜索到 {len(search_results)} 本书籍", "success")
        
        print_status("API功能测试完成", "success")
        return True
        
    except Exception as e:
        print_status(f"API测试失败: {e}", "error")
        return False


async def test_mcp_tools():
    """测试MCP工具"""
    print_status("测试MCP工具功能")
    print("=" * 50)
    
    try:
        from wechat_reading_mcp.config import Config
        from wechat_reading_mcp.wechat_api import WeChatReadingAPI
        from wechat_reading_mcp.tools import (
            get_book_list_tool,
            get_book_details_tool,
            get_notes_tool,
            search_books_tool
        )
        
        config = Config()
        api = WeChatReadingAPI(config)
        
        # 测试书籍列表工具
        print_status("测试书籍列表工具...")
        result = await get_book_list_tool(api, {"status": "all", "limit": 2})
        if result.get("success"):
            print_status("书籍列表工具测试成功", "success")
        else:
            print_status("书籍列表工具测试失败", "error")
        
        # 测试搜索工具
        print_status("测试搜索工具...")
        result = await search_books_tool(api, {"query": "三体", "limit": 2})
        if result.get("success"):
            print_status("搜索工具测试成功", "success")
        else:
            print_status("搜索工具测试失败", "error")
        
        # 测试笔记工具
        print_status("测试笔记工具...")
        result = await get_notes_tool(api, {"limit": 2})
        if result.get("success"):
            print_status("笔记工具测试成功", "success")
        else:
            print_status("笔记工具测试失败", "error")
        
        await api.close()
        print_status("MCP工具测试完成", "success")
        return True
        
    except Exception as e:
        print_status(f"MCP工具测试失败: {e}", "error")
        return False


async def test_mcp_server():
    """测试MCP服务器"""
    print_status("测试MCP服务器")
    print("=" * 50)
    
    try:
        # 启动MCP服务器进程
        print_status("启动MCP服务器...")
        
        process = subprocess.Popen(
            [sys.executable, "-m", "wechat_reading_mcp.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
        if process.poll() is not None:
            # 进程已退出，读取错误信息
            stdout, stderr = process.communicate()
            print_status("服务器启动失败", "error")
            print_status(f"错误信息: {stderr}", "error")
            return False
        
        print_status("服务器启动成功", "success")
        
        # 测试工具列表请求
        print_status("测试工具列表请求...")
        
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list"
        }
        
        try:
            process.stdin.write(json.dumps(list_tools_request) + "\n")
            process.stdin.flush()
            
            # 等待响应
            await asyncio.sleep(1)
            
            print_status("工具列表请求发送成功", "success")
            
        except Exception as e:
            print_status(f"工具列表请求失败: {e}", "error")
        
        # 停止服务器
        print_status("停止服务器...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        
        print_status("MCP服务器测试完成", "success")
        return True
        
    except Exception as e:
        print_status(f"MCP服务器测试失败: {e}", "error")
        return False


def run_pytest():
    """运行pytest测试"""
    print_status("运行pytest测试")
    print("=" * 50)
    
    if os.path.exists("tests"):
        result = subprocess.run([sys.executable, "-m", "pytest", "tests/", "-v"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print_status("pytest测试通过", "success")
            return True
        else:
            print_status("pytest测试失败", "error")
            print(result.stdout)
            print(result.stderr)
            return False
    else:
        print_status("未找到tests目录", "warning")
        return True


async def main():
    """主函数"""
    print("微信读书MCP服务器测试套件")
    print("=" * 60)
    
    # 检查环境
    if not os.path.exists(".env"):
        print_status("请先运行 python run.py 配置环境", "error")
        return False
    
    all_passed = True
    
    # 运行API测试
    if not await test_api_functions():
        all_passed = False
    
    print()
    
    # 运行MCP工具测试
    if not await test_mcp_tools():
        all_passed = False
    
    print()
    
    # 运行MCP服务器测试
    if not await test_mcp_server():
        all_passed = False
    
    print()
    
    # 运行pytest测试
    if not run_pytest():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print_status("所有测试通过！", "success")
    else:
        print_status("部分测试失败", "error")
    
    return all_passed


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print_status("测试被用户中断", "warning")
        sys.exit(1)
    except Exception as e:
        print_status(f"测试过程中出现错误: {e}", "error")
        import traceback
        traceback.print_exc()
        sys.exit(1)
