#!/usr/bin/env python3
"""
微信读书Cookie获取助手

提供交互式的Cookie获取和验证工具。
"""

import os
import sys
import re
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🍪 微信读书Cookie获取助手")
    print("=" * 60)


def print_instructions():
    """打印获取Cookie的详细说明"""
    print("\n📋 获取Cookie的详细步骤：")
    print("\n1️⃣  打开浏览器并访问微信读书")
    print("   - 在浏览器中访问：https://weread.qq.com/")
    print("   - 使用微信扫码登录")
    
    print("\n2️⃣  打开开发者工具")
    print("   - 按F12键（或右键点击页面选择'检查'）")
    print("   - 开发者工具会在页面底部或右侧打开")
    
    print("\n3️⃣  切换到Network标签")
    print("   - 在开发者工具中点击'Network'（网络）标签")
    print("   - 如果看不到，点击'>>'展开更多标签")
    
    print("\n4️⃣  刷新页面并查找请求")
    print("   - 按F5刷新页面")
    print("   - 在左侧请求列表中找到weread.qq.com的请求")
    print("   - 点击任意一个请求")
    
    print("\n5️⃣  复制Cookie")
    print("   - 在右侧面板点击'Headers'（请求头）")
    print("   - 找到'Request Headers'部分")
    print("   - 找到'Cookie:'这一行")
    print("   - 复制Cookie后面的完整内容")
    
    print("\n" + "=" * 60)


def validate_cookie_format(cookie):
    """验证Cookie格式"""
    if not cookie or not isinstance(cookie, str):
        return False, "Cookie不能为空"
    
    # 检查基本格式
    if "=" not in cookie:
        return False, "Cookie格式不正确，应包含键值对"
    
    # 检查是否包含微信读书相关字段
    required_patterns = [
        r'wr_vid=\d+',  # 用户ID
        r'wr_skey=[\w\-]+',  # 会话密钥
    ]
    
    missing_fields = []
    for pattern in required_patterns:
        if not re.search(pattern, cookie):
            field_name = pattern.split('=')[0]
            missing_fields.append(field_name)
    
    if missing_fields:
        return False, f"Cookie中缺少必要字段: {', '.join(missing_fields)}"
    
    return True, "Cookie格式验证通过"


async def test_cookie(cookie):
    """测试Cookie是否有效"""
    print("\n🔍 测试Cookie有效性...")
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Cookie": cookie,
        "Accept": "application/json, text/plain, */*",
        "Referer": "https://weread.qq.com/",
    }
    
    try:
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # 测试访问书架API
            async with session.get("https://weread.qq.com/web/shelf", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if "books" in data or "err" not in data:
                        print("✅ Cookie测试成功！可以正常访问微信读书API")
                        return True
                    else:
                        print("❌ Cookie可能已过期或无效")
                        return False
                else:
                    print(f"❌ API请求失败，状态码: {response.status}")
                    return False
                    
    except asyncio.TimeoutError:
        print("❌ 请求超时，请检查网络连接")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def save_cookie_to_env(cookie):
    """保存Cookie到.env文件"""
    env_path = project_root / ".env"
    
    # 读取现有的.env文件内容
    env_content = ""
    if env_path.exists():
        with open(env_path, "r", encoding="utf-8") as f:
            env_content = f.read()
    
    # 更新或添加Cookie配置
    cookie_line = f"WECHAT_READING_COOKIE={cookie}"
    
    if "WECHAT_READING_COOKIE=" in env_content:
        # 替换现有的Cookie配置
        env_content = re.sub(
            r"WECHAT_READING_COOKIE=.*",
            cookie_line,
            env_content
        )
    else:
        # 添加新的Cookie配置
        if env_content and not env_content.endswith("\n"):
            env_content += "\n"
        env_content += cookie_line + "\n"
    
    # 写入文件
    with open(env_path, "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print(f"✅ Cookie已保存到 {env_path}")


def extract_user_info(cookie):
    """从Cookie中提取用户信息"""
    info = {}
    
    # 提取用户ID
    vid_match = re.search(r'wr_vid=(\d+)', cookie)
    if vid_match:
        info['user_id'] = vid_match.group(1)
    
    # 提取用户名
    name_match = re.search(r'wr_name=([^;]+)', cookie)
    if name_match:
        info['username'] = name_match.group(1)
    
    return info


async def main():
    """主函数"""
    print_banner()
    print_instructions()
    
    while True:
        print("\n" + "=" * 60)
        print("请选择操作：")
        print("1. 输入Cookie进行验证和保存")
        print("2. 重新显示获取Cookie的说明")
        print("3. 测试现有的Cookie配置")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n📝 请粘贴你从浏览器开发者工具中复制的Cookie：")
            print("（提示：Cookie通常很长，包含多个键值对，以分号分隔）")
            cookie = input("\nCookie: ").strip()
            
            if not cookie:
                print("❌ Cookie不能为空")
                continue
            
            # 验证Cookie格式
            is_valid, message = validate_cookie_format(cookie)
            print(f"\n📋 格式验证: {message}")
            
            if not is_valid:
                print("💡 请确保复制了完整的Cookie内容")
                continue
            
            # 提取用户信息
            user_info = extract_user_info(cookie)
            if user_info:
                print("\n👤 检测到的用户信息:")
                for key, value in user_info.items():
                    print(f"   {key}: {value}")
            
            # 测试Cookie
            is_working = await test_cookie(cookie)
            
            if is_working:
                # 保存Cookie
                save_cookie_to_env(cookie)
                print("\n🎉 Cookie配置完成！现在可以使用微信读书MCP服务器了")
                print("\n下一步：")
                print("   python examples/demo.py  # 运行演示脚本")
                print("   python -m wechat_reading_mcp.server  # 启动MCP服务器")
                break
            else:
                print("\n💡 建议：")
                print("   1. 确保已经登录微信读书网页版")
                print("   2. 重新获取Cookie")
                print("   3. 检查网络连接")
        
        elif choice == "2":
            print_instructions()
        
        elif choice == "3":
            env_path = project_root / ".env"
            if not env_path.exists():
                print("❌ 未找到.env文件")
                continue
            
            with open(env_path, "r", encoding="utf-8") as f:
                env_content = f.read()
            
            cookie_match = re.search(r"WECHAT_READING_COOKIE=(.+)", env_content)
            if not cookie_match:
                print("❌ .env文件中未找到Cookie配置")
                continue
            
            cookie = cookie_match.group(1).strip()
            if cookie == "your_cookie_here":
                print("❌ 请先配置真实的Cookie")
                continue
            
            await test_cookie(cookie)
        
        elif choice == "4":
            print("\n👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请输入1-4")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出现错误: {e}")
        import traceback
        traceback.print_exc()
