#!/usr/bin/env python3
"""
微信读书MCP服务器快速启动脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def print_status(message, status="info"):
    """打印状态信息"""
    icons = {"info": "ℹ️", "success": "✅", "error": "❌", "warning": "⚠️"}
    print(f"{icons.get(status, 'ℹ️')} {message}")


def check_and_create_env():
    """检查并创建.env文件"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print_status("已创建.env文件", "success")
            print_status("请编辑.env文件，将示例Cookie替换为你的真实Cookie", "warning")
            return False
        else:
            print_status("未找到.env.example文件", "error")
            return False
    
    # 检查Cookie配置
    with open(".env", "r", encoding="utf-8") as f:
        content = f.read()
    
    if "WECHAT_READING_COOKIE=" not in content:
        print_status("未找到Cookie配置", "error")
        return False
    
    # 检查是否使用了示例Cookie
    if "_clck=q6et1q|1|fxc|0" in content:
        print_status("检测到示例Cookie，请替换为你的真实Cookie", "warning")
        return False
    
    return True


def install_dependencies():
    """安装依赖"""
    print_status("检查并安装依赖...")
    
    try:
        # 检查是否已安装mcp
        import mcp
        print_status("依赖已安装", "success")
        return True
    except ImportError:
        print_status("正在安装依赖...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print_status("依赖安装成功", "success")
            return True
        else:
            print_status(f"依赖安装失败: {result.stderr}", "error")
            return False


def start_mcp_server():
    """启动MCP服务器"""
    print_status("启动微信读书MCP服务器...")
    print_status("服务器启动后，可以在Claude Desktop中使用", "info")
    print_status("按Ctrl+C停止服务器", "info")
    print()
    
    try:
        # 启动服务器
        subprocess.run([sys.executable, "-m", "wechat_reading_mcp.server"])
    except KeyboardInterrupt:
        print()
        print_status("服务器已停止", "info")
    except Exception as e:
        print_status(f"服务器启动失败: {e}", "error")


def main():
    """主函数"""
    print("🚀 微信读书MCP服务器快速启动")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print_status("需要Python 3.8或更高版本", "error")
        return
    
    # 检查并创建.env文件
    if not check_and_create_env():
        print()
        print("📋 获取Cookie的方法：")
        print("1. 运行: python tools/cookie_helper.py")
        print("2. 或访问: https://weread.qq.com/ 登录后使用浏览器书签工具")
        print("3. 或查看: docs/获取Cookie指南.md")
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 启动服务器
    start_mcp_server()


if __name__ == "__main__":
    main()
