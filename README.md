# 📚 微信读书 MCP 服务器

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![MCP](https://img.shields.io/badge/MCP-Compatible-orange.svg)](https://modelcontextprotocol.io)

一个为AI模型提供微信读书数据访问能力的MCP（Model Context Protocol）服务器实现。让Claude等AI助手能够直接访问你的微信读书数据，包括书籍列表、阅读笔记、搜索书籍等功能。

## 📑 目录

- [✨ 功能特性](#-功能特性)
- [🎯 使用场景](#-使用场景)
- [🚀 快速开始](#-快速开始)
  - [📋 系统要求](#-系统要求)
  - [⚡ 一键启动](#-一键启动推荐新手)
  - [📋 分步安装](#-分步安装推荐开发者)
- [🍪 获取微信读书Cookie](#-获取微信读书cookie)
- [🤖 在Claude Desktop中使用](#-在claude-desktop中使用)
- [🛠️ 可用工具](#️-可用工具)
- [💡 使用示例](#-使用示例)
- [🧪 开发和测试](#-开发和测试)
- [⚠️ 注意事项](#️-注意事项)
- [🆘 故障排除](#-故障排除)
- [🤝 贡献指南](#-贡献指南)

## ✨ 功能特性

- 📚 **书籍管理**: 获取在读、已读、想读书籍列表，支持状态筛选
- 📖 **书籍详情**: 查询书籍详细信息，包括作者、评分、简介、阅读进度等
- 📝 **笔记系统**: 获取读书笔记和划线内容，支持按书籍筛选
- 🔍 **智能搜索**: 支持按书名、作者等关键词搜索微信读书全库
- 🔐 **安全认证**: 基于Cookie的安全认证，保护用户隐私
- 📊 **详细日志**: 完整的日志记录，便于调试和监控
- 🚀 **一键启动**: 自动化安装配置，快速上手
- 🛠️ **开发友好**: 完整的测试套件和开发工具

## 🎯 使用场景

- **阅读助手**: 让AI帮你整理和分析读书笔记
- **书籍推荐**: 基于阅读历史获得个性化推荐
- **阅读统计**: 分析阅读习惯和进度
- **笔记整理**: 自动整理和分类读书笔记
- **书籍搜索**: 快速查找感兴趣的书籍

## 🚀 快速开始

### 📋 系统要求

- **Python**: 3.8 或更高版本
- **操作系统**: Windows、macOS、Linux
- **网络**: 能够访问微信读书网页版
- **浏览器**: 用于获取Cookie（Chrome、Firefox、Safari等）

### ⚡ 一键启动（推荐新手）

```bash
# 1. 克隆项目
git clone https://github.com/your-username/wechat-reading-mcp.git
cd wechat-reading-mcp

# 2. 一键启动（自动安装依赖、配置环境、启动服务器）
python run.py --quick
```

这个命令会自动：
- ✅ 检查Python版本
- ✅ 安装所需依赖
- ✅ 创建配置文件
- ✅ 引导Cookie配置
- ✅ 启动MCP服务器

### 📋 分步安装（推荐开发者）

如果你喜欢分步操作或需要自定义配置：

```bash
# 启动交互式管理工具
python run.py

# 菜单选项说明：
# 0. ⚡ 快速启动 - 一键配置并启动
# 1. 🍪 配置Cookie - 设置微信读书认证
# 2. 🧪 运行功能演示 - 测试API功能
# 3. 🚀 启动MCP服务器 - 启动服务
# 4. 🔧 安装/更新依赖 - 管理依赖包
# 5. 🧹 运行测试 - 执行测试套件
# 6. 📋 生成Claude配置 - 自动生成配置文件
# 7. 📖 查看文档 - 查看帮助文档
```

### 🆘 获取帮助

```bash
# 查看命令行选项
python run.py --help

# 查看项目文档
python run.py  # 选择选项7
```

## 🍪 获取微信读书Cookie

Cookie是访问微信读书数据的关键凭证。我们提供了多种获取方式：

### 方法一：Cookie获取助手（🌟 推荐新手）

```bash
python tools/cookie_helper.py
```

**特点**：
- ✅ 交互式引导，零技术门槛
- ✅ 自动验证Cookie有效性
- ✅ 自动保存到配置文件
- ✅ 提供详细的操作说明

### 方法二：浏览器书签工具（⚡ 最快速）

```bash
# 1. 查看书签代码
cat tools/bookmarklet.js

# 2. 按照以下步骤操作：
```

**操作步骤**：
1. 访问 [微信读书网页版](https://weread.qq.com/) 并登录
2. 复制 `tools/bookmarklet.js` 文件中的JavaScript代码
3. 在浏览器地址栏粘贴代码并按回车
4. 弹出对话框会显示你的Cookie，点击复制即可
5. 将Cookie粘贴到 `.env` 文件中

### 方法三：手动获取（🔧 传统方法）

**适用场景**：其他方法无法使用时的备选方案

**操作步骤**：
1. 打开浏览器，访问 [微信读书网页版](https://weread.qq.com/)
2. 登录你的微信读书账号
3. 打开浏览器开发者工具（按F12键）
4. 切换到 "Network"（网络）标签页
5. 刷新页面，找到任意一个请求
6. 在请求头中找到 `Cookie` 字段
7. 复制完整的Cookie值到 `.env` 文件

### 📖 详细图文教程

查看 [`docs/获取Cookie指南.md`](docs/获取Cookie指南.md) 获取详细的图文说明。

### ⚠️ Cookie安全提醒

- 🔒 Cookie包含敏感信息，请妥善保管
- 🚫 不要在公共场所或不安全的网络环境下获取
- 🔄 定期更新Cookie以确保安全性
- 📝 不要将Cookie分享给他人

## 🤖 在Claude Desktop中使用

### 🎯 自动配置（推荐）

使用我们的配置生成工具，自动创建Claude Desktop配置：

```bash
# 生成并安装Claude Desktop配置
python tools/claude_config_generator.py

# 或通过管理工具
python run.py  # 选择选项6
```

**配置生成器功能**：
- ✅ 自动检测项目路径
- ✅ 生成正确的配置格式
- ✅ 合并现有配置（不覆盖其他MCP服务器）
- ✅ 提供配置预览
- ✅ 自动保存到正确位置

### 🔧 手动配置

如果需要手动配置，在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "wechat-reading": {
      "command": "python",
      "args": ["-m", "wechat_reading_mcp.server"],
      "cwd": "/path/to/wechat-reading-mcp",
      "env": {
        "WECHAT_READING_COOKIE": "your_cookie_here"
      }
    }
  }
}
```

**配置文件位置**：
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

### 🚀 启动服务器

```bash
# 方法1：交互式管理（推荐）
python run.py  # 选择选项3

# 方法2：直接启动
python -m wechat_reading_mcp.server

# 方法3：快速启动（包含配置检查）
python run.py --quick
```

### 🔄 重启Claude Desktop

配置完成后，重启Claude Desktop以加载MCP服务器。

### ✅ 验证连接

在Claude Desktop中输入以下内容来测试连接：

```
请帮我获取我的微信读书书籍列表
```

如果配置成功，Claude会显示你的书籍信息。

## 🛠️ 可用工具

本MCP服务器提供4个核心工具，让AI助手能够访问你的微信读书数据：

### 📚 1. get_book_list - 获取书籍列表

获取用户的阅读书籍列表，支持按状态筛选。

**参数说明：**
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `status` | string | 否 | `"all"` | 书籍状态筛选 |
| `limit` | integer | 否 | `20` | 返回数量限制 |

**status可选值：**
- `"reading"` - 正在阅读的书籍
- `"finished"` - 已完成阅读的书籍
- `"wishlist"` - 想要阅读的书籍
- `"all"` - 所有书籍（默认）

**使用示例：**
```json
{
  "status": "reading",
  "limit": 10
}
```

**返回数据包含：**
- 书籍ID、标题、作者
- 封面图片、分类、评分
- 阅读状态、进度、时间

### 📖 2. get_book_details - 获取书籍详细信息

获取指定书籍的完整详细信息。

**参数说明：**
| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `book_id` | string | 是 | 书籍唯一标识符 |

**使用示例：**
```json
{
  "book_id": "123456"
}
```

**返回数据包含：**
- 基本信息：标题、作者、出版社、ISBN
- 内容信息：简介、字数、章节数
- 评价信息：评分、评价数量
- 阅读信息：阅读进度、阅读时间

### 📝 3. get_notes - 获取笔记和划线

获取用户的读书笔记和划线内容，支持按书籍筛选。

**参数说明：**
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `book_id` | string | 否 | - | 特定书籍ID，不指定则获取所有 |
| `limit` | integer | 否 | `50` | 返回数量限制（最大200） |

**使用示例：**
```json
{
  "book_id": "123456",
  "limit": 20
}
```

**返回数据包含：**
- 笔记内容和划线文本
- 笔记类型和颜色标记
- 创建时间和页码位置
- 所属书籍和章节信息

### 🔍 4. search_books - 搜索书籍

在微信读书全库中搜索书籍。

**参数说明：**
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `query` | string | 是 | - | 搜索关键词 |
| `limit` | integer | 否 | `20` | 返回数量限制（最大50） |

**搜索支持：**
- 📖 书名搜索
- 👤 作者搜索
- 🏷️ 关键词搜索
- 🔤 模糊匹配

**使用示例：**
```json
{
  "query": "人工智能",
  "limit": 10
}
```

**返回数据包含：**
- 书籍基本信息
- 搜索相关度排序
- 价格和出版信息

## 💡 使用示例

### 在Claude中的对话示例

```
用户：请帮我获取我正在阅读的书籍列表

Claude：我来帮你获取正在阅读的书籍列表。

[调用 get_book_list 工具]

你目前正在阅读以下书籍：
1. 《认知觉醒：开启自我改变的原动力》- 周岭
2. 《深度学习入门：基于Python的理论与实现》- 斋藤康毅
3. 《硅谷钢铁侠：埃隆·马斯克的冒险人生》- 阿什利·万斯

需要我帮你查看某本书的详细信息或读书笔记吗？
```

```
用户：帮我搜索关于人工智能的书籍

Claude：我来为你搜索人工智能相关的书籍。

[调用 search_books 工具]

找到以下人工智能相关书籍：
1. 《AI：人工智能的本质与未来》- 玛格丽特·博登
2. 《智人之上：从石器时代到AI时代的信息网络简史》- 尤瓦尔·赫拉利
3. 《大模型应用开发 动手做AI Agent》- 黄佳

你对哪本书感兴趣？我可以帮你查看详细信息。
```

## 🧪 开发和测试

### 🔬 运行完整测试套件

```bash
# 运行所有测试（API测试、MCP工具测试、服务器测试）
python scripts/test.py

# 或使用管理工具
python run.py  # 选择选项5
```

**测试套件包含：**
- ✅ API功能测试
- ✅ MCP工具测试
- ✅ 服务器连接测试
- ✅ 配置验证测试

### 🧪 运行单元测试

```bash
# 运行pytest测试
pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_wechat_api.py -v

# 生成测试覆盖率报告
pytest tests/ --cov=src/wechat_reading_mcp --cov-report=html
```

### 🎨 代码格式化

```bash
# 格式化所有Python文件
black src/ tests/ scripts/ tools/

# 检查格式但不修改
black --check src/ tests/ scripts/ tools/
```

### 🔍 代码质量检查

```bash
# 代码风格检查
flake8 src/ tests/ scripts/ tools/

# 类型检查（如果使用了类型注解）
mypy src/wechat_reading_mcp/

# 安全检查
bandit -r src/
```

### 📁 项目结构

```
wechat-reading-mcp/
├── 📂 src/wechat_reading_mcp/     # 🔧 核心代码
│   ├── __init__.py               # 包初始化
│   ├── server.py                 # MCP服务器主程序
│   ├── config.py                 # 配置管理
│   ├── wechat_api.py            # 微信读书API客户端
│   └── tools.py                  # MCP工具实现
├── 📂 scripts/                   # 🛠️ 管理脚本
│   ├── setup.py                 # 安装和配置脚本
│   └── test.py                   # 完整测试套件
├── 📂 tools/                     # 🔨 工具脚本
│   ├── cookie_helper.py         # Cookie获取助手
│   ├── claude_config_generator.py # 配置生成器
│   └── bookmarklet.js           # 浏览器书签工具
├── 📂 docs/                      # 📚 文档
│   └── 获取Cookie指南.md         # Cookie获取图文教程
├── 📂 tests/                     # 🧪 单元测试
│   ├── __init__.py
│   └── test_wechat_api.py       # API测试
├── 📂 examples/                  # 💡 示例代码
│   └── demo.py                   # 功能演示脚本
├── 📂 logs/                      # 📋 日志文件
├── 📄 run.py                     # 🎯 主管理脚本
├── 📄 README.md                  # 📖 项目文档
├── 📄 requirements.txt           # 📦 依赖列表
├── 📄 pyproject.toml            # ⚙️ 项目配置
├── 📄 CHANGELOG.md              # 📝 变更日志
└── 📄 .gitignore                # 🚫 Git忽略文件
```

## ⚠️ 注意事项

### 🔐 安全和隐私

- **Cookie安全**: Cookie包含敏感信息，请妥善保管，不要泄露给他人
- **定期更新**: Cookie有一定的有效期，建议定期更新以确保安全性
- **网络安全**: 不要在公共网络环境下获取或使用Cookie
- **数据隐私**: 本工具仅在本地处理数据，不会上传到第三方服务器

### 📊 使用规范

- **请求频率**: 请合理控制请求频率，避免对微信读书服务器造成过大压力
- **使用条款**: 请遵守微信读书的使用条款和相关法律法规
- **个人使用**: 本工具仅供个人学习和使用，请勿用于商业用途
- **数据备份**: 建议定期备份重要的读书笔记和数据

### 🔧 技术要求

- **Python版本**: 需要Python 3.8或更高版本
- **网络连接**: 需要稳定的网络连接访问微信读书服务
- **存储空间**: 建议预留至少100MB的磁盘空间用于日志和缓存

## 🆘 故障排除

### 🔍 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| **认证失败** | Cookie过期或无效 | 重新获取Cookie并更新配置 |
| **网络错误** | 网络连接问题 | 检查网络连接和防火墙设置 |
| **数据为空** | 账号无数据或权限不足 | 确认账号中有相应数据 |
| **服务器启动失败** | 依赖缺失或配置错误 | 运行 `python run.py` 重新配置 |
| **Claude连接失败** | MCP配置错误 | 使用配置生成器重新生成配置 |

### 📋 日志查看

服务器会记录详细的运行信息，可以通过以下方式查看日志：

```bash
# 查看实时日志
tail -f logs/wechat_reading_mcp.log

# 查看最近的日志
cat logs/wechat_reading_mcp.log | tail -100

# 搜索错误日志
grep "ERROR" logs/wechat_reading_mcp.log
```

### 🔧 调试模式

启用调试模式获取更详细的日志信息：

```bash
# 设置调试级别
export LOG_LEVEL=DEBUG

# 启动服务器
python -m wechat_reading_mcp.server
```

### 🆘 获取帮助

如果遇到问题，可以：

1. 📖 查看 [获取Cookie指南](docs/获取Cookie指南.md)
2. ❓ 查看 [常见问题解答](docs/FAQ.md)
3. 🔍 搜索 [Issues](https://github.com/your-username/wechat-reading-mcp/issues)
4. 💬 提交新的 [Issue](https://github.com/your-username/wechat-reading-mcp/issues/new)
5. 📧 联系维护者

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 🎯 贡献方式

- 🐛 **报告Bug**: 提交详细的bug报告
- 💡 **功能建议**: 提出新功能想法
- 📝 **文档改进**: 完善文档和教程
- 🔧 **代码贡献**: 提交代码修复和新功能
- 🌍 **翻译**: 帮助翻译文档到其他语言

### 📋 开发流程

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 🧪 贡献要求

- ✅ 代码需要通过所有测试
- ✅ 遵循项目的代码风格
- ✅ 添加适当的文档和注释
- ✅ 更新相关的测试用例

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

<div align="center">

**如果这个项目对你有帮助，请给它一个 ⭐ Star！**

[🐛 报告Bug](https://github.com/your-username/wechat-reading-mcp/issues) •
[💡 功能建议](https://github.com/your-username/wechat-reading-mcp/issues) •
[📖 文档](https://github.com/your-username/wechat-reading-mcp/wiki) •
[💬 讨论](https://github.com/your-username/wechat-reading-mcp/discussions)

</div>
