Metadata-Version: 2.4
Name: wechat-reading-mcp
Version: 0.1.0
Summary: 微信读书MCP服务器 - 为AI模型提供微信读书数据访问能力
Author-email: Your Name <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/yourusername/wechat-reading-mcp
Project-URL: Repository, https://github.com/yourusername/wechat-reading-mcp
Project-URL: Issues, https://github.com/yourusername/wechat-reading-mcp/issues
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: mcp>=1.0.0
Requires-Dist: requests>=2.31.0
Requires-Dist: aiohttp>=3.9.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: loguru>=0.7.0
Requires-Dist: python-dateutil>=2.8.0
Requires-Dist: cryptography>=41.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"

# 微信读书 MCP 服务器

一个为AI模型提供微信读书数据访问能力的MCP（Model Context Protocol）服务器实现。

## 功能特性

- 📚 **书籍列表获取**: 获取用户的在读、已读、想读书籍列表
- 📖 **书籍详细信息**: 查询书籍的详细信息，包括作者、评分、阅读进度等
- 📝 **笔记和划线**: 获取用户的读书笔记和划线内容
- 🔍 **书籍搜索**: 支持按书名、作者等关键词搜索书籍
- 🔐 **安全认证**: 支持Cookie认证，保护用户隐私
- 📊 **详细日志**: 完整的日志记录，便于调试和监控

## 安装和配置

### 1. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd wechat-reading-mcp

# 安装依赖
pip install -r requirements.txt

# 或者使用开发模式安装
pip install -e .
```

### 2. 配置认证信息

复制环境变量模板文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的微信读书认证信息：

```env
# 微信读书Cookie（必需）
WECHAT_READING_COOKIE=your_cookie_here

# 其他可选配置
WECHAT_READING_USER_ID=your_user_id_here
REQUEST_TIMEOUT=30
MAX_RETRIES=3
LOG_LEVEL=INFO
```

### 3. 获取微信读书Cookie

我们提供了多种方式来获取Cookie：

#### 方法一：使用Cookie获取助手（推荐）
```bash
python tools/cookie_helper.py
```
这个交互式工具会指导你完成整个Cookie获取过程。

#### 方法二：使用浏览器书签工具（最简单）
1. 访问 [微信读书网页版](https://weread.qq.com/) 并登录
2. 复制 `tools/bookmarklet.js` 文件中的JavaScript代码
3. 在浏览器地址栏粘贴代码并回车
4. 会弹出对话框显示你的Cookie，点击复制即可

#### 方法三：手动获取（传统方法）
1. 打开浏览器，访问 [微信读书网页版](https://weread.qq.com/)
2. 登录你的微信读书账号
3. 打开浏览器开发者工具（F12）
4. 切换到 "Network" 标签页
5. 刷新页面，找到任意一个请求
6. 在请求头中找到 `Cookie` 字段，复制完整的Cookie值
7. 将Cookie值填入 `.env` 文件中的 `WECHAT_READING_COOKIE` 字段

#### 详细图文教程
查看 `docs/获取Cookie指南.md` 文件获取详细的图文说明。

## 使用方法

### 作为MCP服务器运行

```bash
python -m wechat_reading_mcp.server
```

### 在Claude Desktop中配置

在Claude Desktop的配置文件中添加以下配置：

```json
{
  "mcpServers": {
    "wechat-reading": {
      "command": "python",
      "args": ["-m", "wechat_reading_mcp.server"],
      "cwd": "/path/to/wechat-reading-mcp"
    }
  }
}
```

## 可用工具

### 1. get_book_list - 获取书籍列表

获取用户的阅读书籍列表。

**参数：**
- `status` (可选): 书籍状态，可选值：
  - `"reading"`: 在读
  - `"finished"`: 已读
  - `"wishlist"`: 想读
  - `"all"`: 全部（默认）
- `limit` (可选): 返回结果数量限制，默认20，最大100

**示例：**
```json
{
  "status": "reading",
  "limit": 10
}
```

### 2. get_book_details - 获取书籍详细信息

获取指定书籍的详细信息。

**参数：**
- `book_id` (必需): 书籍ID

**示例：**
```json
{
  "book_id": "123456"
}
```

### 3. get_notes - 获取笔记和划线

获取用户的读书笔记和划线内容。

**参数：**
- `book_id` (可选): 书籍ID，不指定则获取所有笔记
- `limit` (可选): 返回结果数量限制，默认50，最大200

**示例：**
```json
{
  "book_id": "123456",
  "limit": 20
}
```

### 4. search_books - 搜索书籍

搜索书籍。

**参数：**
- `query` (必需): 搜索关键词（书名、作者等）
- `limit` (可选): 返回结果数量限制，默认20，最大50

**示例：**
```json
{
  "query": "三体",
  "limit": 10
}
```

## 开发和测试

### 运行测试

```bash
pytest tests/
```

### 代码格式化

```bash
black src/ tests/
```

### 代码检查

```bash
flake8 src/ tests/
```

## 注意事项

1. **Cookie有效期**: 微信读书的Cookie有一定的有效期，如果出现认证失败，请重新获取Cookie
2. **请求频率**: 请合理控制请求频率，避免对微信读书服务器造成过大压力
3. **隐私保护**: Cookie包含敏感信息，请妥善保管，不要泄露给他人
4. **使用条款**: 请遵守微信读书的使用条款和相关法律法规

## 故障排除

### 常见问题

1. **认证失败**: 检查Cookie是否正确配置且未过期
2. **网络错误**: 检查网络连接和防火墙设置
3. **数据为空**: 确认账号中有相应的数据（书籍、笔记等）

### 日志查看

服务器会在控制台和日志文件中记录详细的运行信息，可以通过查看日志来诊断问题：

```bash
tail -f logs/wechat_reading_mcp.log
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
