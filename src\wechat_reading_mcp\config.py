"""
配置管理模块

处理微信读书MCP服务器的配置信息。
"""

import os
from typing import Optional
from pydantic import BaseModel, Field
from loguru import logger


class Config(BaseModel):
    """配置类"""
    
    # 微信读书认证配置
    wechat_reading_cookie: str = Field(
        default="",
        description="微信读书Cookie"
    )
    
    wechat_reading_user_id: Optional[str] = Field(
        default=None,
        description="微信读书用户ID"
    )
    
    # API请求配置
    request_timeout: int = Field(
        default=30,
        description="请求超时时间（秒）"
    )
    
    max_retries: int = Field(
        default=3,
        description="最大重试次数"
    )
    
    retry_delay: float = Field(
        default=1.0,
        description="重试延迟时间（秒）"
    )
    
    # 日志配置
    log_level: str = Field(
        default="INFO",
        description="日志级别"
    )
    
    log_file: Optional[str] = Field(
        default=None,
        description="日志文件路径"
    )
    
    # MCP服务器配置
    mcp_server_name: str = Field(
        default="wechat-reading-mcp",
        description="MCP服务器名称"
    )
    
    mcp_server_version: str = Field(
        default="0.1.0",
        description="MCP服务器版本"
    )
    
    def __init__(self, **kwargs):
        """初始化配置"""
        # 从环境变量加载配置
        env_config = self._load_from_env()
        
        # 合并配置
        merged_config = {**env_config, **kwargs}
        
        super().__init__(**merged_config)
        
        # 验证配置
        self._validate_config()
    
    def _load_from_env(self) -> dict:
        """从环境变量加载配置"""
        return {
            "wechat_reading_cookie": os.getenv("WECHAT_READING_COOKIE", ""),
            "wechat_reading_user_id": os.getenv("WECHAT_READING_USER_ID"),
            "request_timeout": int(os.getenv("REQUEST_TIMEOUT", "30")),
            "max_retries": int(os.getenv("MAX_RETRIES", "3")),
            "retry_delay": float(os.getenv("RETRY_DELAY", "1.0")),
            "log_level": os.getenv("LOG_LEVEL", "INFO"),
            "log_file": os.getenv("LOG_FILE"),
            "mcp_server_name": os.getenv("MCP_SERVER_NAME", "wechat-reading-mcp"),
            "mcp_server_version": os.getenv("MCP_SERVER_VERSION", "0.1.0"),
        }
    
    def _validate_config(self):
        """验证配置"""
        if not self.wechat_reading_cookie:
            logger.warning("未配置微信读书Cookie，某些功能可能无法使用")
        
        if self.request_timeout <= 0:
            raise ValueError("请求超时时间必须大于0")
        
        if self.max_retries < 0:
            raise ValueError("最大重试次数不能小于0")
        
        if self.retry_delay < 0:
            raise ValueError("重试延迟时间不能小于0")
        
        # 创建日志目录
        if self.log_file:
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
    
    @property
    def is_authenticated(self) -> bool:
        """检查是否已配置认证信息"""
        return bool(self.wechat_reading_cookie)
    
    def get_headers(self) -> dict:
        """获取HTTP请求头"""
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
        }
        
        if self.wechat_reading_cookie:
            headers["Cookie"] = self.wechat_reading_cookie
        
        return headers
