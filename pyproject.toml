[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wechat-reading-mcp"
version = "0.1.0"
description = "微信读书MCP服务器 - 为AI模型提供微信读书数据访问能力"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "mcp>=1.0.0",
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "python-dateutil>=2.8.0",
    "cryptography>=41.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/wechat-reading-mcp"
Repository = "https://github.com/yourusername/wechat-reading-mcp"
Issues = "https://github.com/yourusername/wechat-reading-mcp/issues"

[project.scripts]
wechat-reading-mcp = "wechat_reading_mcp.server:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
