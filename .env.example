# 微信读书认证配置
# 请复制此文件为 .env 并填入真实的配置信息

# 微信读书Cookie（从浏览器开发者工具中获取）
WECHAT_READING_COOKIE=_clck=q6et1q|1|fxc|0; wr_fp=2036687576; wr_skey=9d8c0gvF; wr_vid=24418686

# 微信读书用户ID（可选，如果Cookie中包含则自动提取）
WECHAT_READING_USER_ID=your_user_id_here

# API请求配置
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/wechat_reading_mcp.log

# MCP服务器配置
MCP_SERVER_NAME=wechat-reading-mcp
MCP_SERVER_VERSION=0.1.0
